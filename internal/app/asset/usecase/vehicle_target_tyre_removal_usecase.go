package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"fmt"

	"gopkg.in/guregu/null.v4"
)

type VehicleTargetTyreRemovalUseCase struct {
	DB                                 database.DBUsecase
	VehicleTargetTyreRemovalRepository repository.VehicleTargetTyreRemovalRepository
	AssetRepository                    repository.AssetRepository
	AssetVehicleRepository             repository.AssetVehicleRepository
}

func NewVehicleTargetTyreRemovalUseCase(
	DB database.DBUsecase,
	vehicleTargetTyreRemovalRepository repository.VehicleTargetTyreRemovalRepository,
	assetRepository repository.AssetRepository,
	assetVehicleRepository repository.AssetVehicleRepository,
) *VehicleTargetTyreRemovalUseCase {
	return &VehicleTargetTyreRemovalUseCase{
		DB:                                 DB,
		VehicleTargetTyreRemovalRepository: vehicleTargetTyreRemovalRepository,
		AssetRepository:                    assetRepository,
		AssetVehicleRepository:             assetVehicleRepository,
	}
}

func (uc *VehicleTargetTyreRemovalUseCase) CreateVehicleTargetTyreRemoval(ctx context.Context, req dtos.CreateVehicleTargetTyreRemovalReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	// Validate asset IDs exist and belong to the client
	err = uc.validateAssetIDs(ctx, req.AssetIDs, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	removal := &models.VehicleTargetTyreRemoval{
		TargetRTD: req.TargetRTD,
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	err = uc.VehicleTargetTyreRemovalRepository.CreateVehicleTargetTyreRemoval(ctx, tx.DB(), removal)
	if err != nil {
		if errorhandler.IsPgDuplicateConstraintError(err) {
			return nil, errorhandler.ErrBadRequest("target_rtd already exists")
		}
		return nil, err
	}

	// Update asset vehicles to reference this target removal
	err = uc.VehicleTargetTyreRemovalRepository.UpdateAssetVehicleTargetRemovalIDs(ctx, tx.DB(), removal.ID, req.AssetIDs)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Vehicle target tyre removal created successfully",
		ReferenceID: removal.ID,
		Data:        nil,
	}, nil
}

func (uc *VehicleTargetTyreRemovalUseCase) UpdateVehicleTargetTyreRemoval(ctx context.Context, id string, req dtos.UpdateVehicleTargetTyreRemovalReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	// Check if the target removal exists and belongs to the client
	existing, err := uc.VehicleTargetTyreRemovalRepository.GetVehicleTargetTyreRemoval(ctx, uc.DB.DB(), models.VehicleTargetTyreRemovalCondition{
		Where: models.VehicleTargetTyreRemovalWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	// Validate asset IDs exist and belong to the client
	err = uc.validateAssetIDs(ctx, req.AssetIDs, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	removal := &models.VehicleTargetTyreRemoval{
		TargetRTD: req.TargetRTD,
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	if existing.TargetRTD != req.TargetRTD {
		err = uc.VehicleTargetTyreRemovalRepository.UpdateVehicleTargetTyreRemoval(ctx, tx.DB(), id, removal)
		if err != nil {
			return nil, err
		}
	}

	// Update asset vehicles to reference this target removal
	err = uc.VehicleTargetTyreRemovalRepository.UpdateAssetVehicleTargetRemovalIDs(ctx, tx.DB(), id, req.AssetIDs)
	if err != nil {
		return nil, err
	}

	// Case delete set to default zero
	if len(req.DeletedAssetIDs) > 0 {
		defaultTarget, err := uc.VehicleTargetTyreRemovalRepository.GetVehicleTargetTyreRemoval(ctx, tx.DB(), models.VehicleTargetTyreRemovalCondition{
			Where: models.VehicleTargetTyreRemovalWhere{
				ClientID:  claim.GetLoggedInClientID(),
				TargetRTD: null.FloatFrom(0),
			},
		})
		if err != nil {
			return nil, err
		}

		err = uc.VehicleTargetTyreRemovalRepository.UpdateAssetVehicleTargetRemovalIDs(ctx, tx.DB(), defaultTarget.ID, req.DeletedAssetIDs)
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Vehicle target tyre removal updated successfully",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *VehicleTargetTyreRemovalUseCase) GetVehicleTargetTyreRemovalList(ctx context.Context, req dtos.VehicleTargetTyreRemovalListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	req.Normalize()

	param := models.GetVehicleTargetTyreRemovalListParam{
		ListRequest: req.ListRequest,
		Cond: models.VehicleTargetTyreRemovalCondition{
			Where: models.VehicleTargetTyreRemovalWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.VehicleTargetTyreRemovalPreload{
				AssetVehicles: true,
			},
		},
	}

	totalRecords, removals, err := uc.VehicleTargetTyreRemovalRepository.GetVehicleTargetTyreRemovalList(ctx, uc.DB.DB(), param)
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: totalRecords,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         dtos.BuildVehicleTargetTyreRemovalListResp(removals),
	}, nil
}

func (uc *VehicleTargetTyreRemovalUseCase) GetVehicleTargetTyreRemovalByID(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	removal, err := uc.VehicleTargetTyreRemovalRepository.GetVehicleTargetTyreRemoval(ctx, uc.DB.DB(), models.VehicleTargetTyreRemovalCondition{
		Where: models.VehicleTargetTyreRemovalWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.VehicleTargetTyreRemovalPreload{
			AssetVehicles: true,
		},
	})
	if err != nil {
		return nil, err
	}
	if removal == nil {
		return nil, errorhandler.ErrDataNotFound("vehicle target tyre removal")
	}

	var assetIDs []string

	for _, assetVehicle := range removal.AssetVehicles {
		assetIDs = append(assetIDs, assetVehicle.AssetID)
	}

	responseData := dtos.VehicleTargetTyreRemovalDetailResp{
		ID:        removal.ID,
		TargetRTD: removal.TargetRTD,
		AssetIDs:  assetIDs,
		CreatedAt: removal.CreatedAt,
		UpdatedAt: removal.UpdatedAt,
		CreatedBy: removal.CreatedBy,
		UpdatedBy: removal.UpdatedBy,
	}

	return &commonmodel.DetailResponse{
		Success: true,
		Message: "Vehicle target tyre removal retrieved successfully",
		Data:    responseData,
	}, nil
}

func (uc *VehicleTargetTyreRemovalUseCase) validateAssetIDs(ctx context.Context, assetIDs []string, clientID string) error {
	if len(assetIDs) == 0 {
		return errorhandler.ErrBadRequest("asset_ids cannot be empty")
	}

	// Check if all asset IDs exist and belong to the client
	assets, err := uc.AssetRepository.GetAssetsByIDs(ctx, uc.DB.DB(), assetIDs)
	if err != nil {
		return err
	}

	if len(assets) != len(assetIDs) {
		return errorhandler.ErrBadRequest("some asset IDs do not exist")
	}

	// Verify all assets belong to the client and are vehicles
	for _, asset := range assets {
		if asset.ClientID != clientID {
			return errorhandler.ErrBadRequest(fmt.Sprintf("asset %s does not belong to client", asset.ID))
		}
		// You might want to add additional validation here to ensure these are vehicle assets
	}

	return nil
}

func (uc *VehicleTargetTyreRemovalUseCase) CheckTargetRTDAvailability(ctx context.Context, req dtos.CheckTargetRTDAvailabilityReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	targetRemoval, err := uc.VehicleTargetTyreRemovalRepository.GetVehicleTargetTyreRemoval(ctx, uc.DB.DB(), models.VehicleTargetTyreRemovalCondition{
		Where: models.VehicleTargetTyreRemovalWhere{
			TargetRTD: null.FloatFrom(req.TargetRTD),
			ClientID:  claim.GetLoggedInClientID(),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	resp := dtos.CheckTargetRTDAvailabilityResp{
		Available: targetRemoval == nil,
		TargetRTD: req.TargetRTD,
	}

	if targetRemoval != nil {
		resp.ID = null.StringFrom(targetRemoval.ID)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        resp,
	}, nil
}
