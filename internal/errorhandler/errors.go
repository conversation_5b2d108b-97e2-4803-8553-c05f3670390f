package errorhandler

import (
	"errors"
	"fmt"
	"strings"

	"github.com/jackc/pgconn"
)

var (
	ErrUserAlreadyRegistered error = fmt.<PERSON><PERSON><PERSON>("email already exists")
	ErrNotEligible           error = fmt.<PERSON><PERSON><PERSON>("you are not eligible to process the request")
	ErrUnauthorized          error = fmt.<PERSON><PERSON><PERSON>("unauthorized")
)

const (
	ErrExceedMaxRow string = "NUM_OF_ROWS_SHOULD_NOT_EXCEED_100"
	ErrNoPermission string = "NO_PERMISSION"
)

type errNotFound struct {
	name string
}

func (e errNotFound) Error() string {
	return fmt.Sprintf("%s_NOT_FOUND", strings.ToUpper(strings.ReplaceAll(e.name, " ", "_")))
}

func ErrDataNotFound(name string) error {
	return errNotFound{name: name}
}

type errAlreadyExist struct {
	name string
}

func (e errAlreadyExist) Error() string {
	return fmt.Sprintf("%s already exist", e.name)
}

func ErrDataAlreadyExist(name string) error {
	return errAlreadyExist{name: name}
}

type errNotAllowed struct {
	message string
}

func (e errNotAllowed) Error() string {
	return e.message
}

func ErrNotAllowed(message string) error {
	return errNotAllowed{message: message}
}

type errBadRequest struct {
	message string
}

func (e errBadRequest) Error() string {
	return e.message
}

func ErrBadRequest(message string) error {
	return errBadRequest{message: message}
}

func ErrInternalServerError(message string) error {
	return errInternalServerError{message: message}
}

type errInternalServerError struct {
	message string
}

func (e errInternalServerError) Error() string {
	return e.message
}

func IsErrNotFound(err error) bool {
	_, ok := err.(errNotFound)
	return ok
}

type ErrExternal struct {
	ExternalCode       string      `json:"external_code"`
	InternalMessage    string      `json:"internal_message"`
	ExternalStatusCode int         `json:"external_status_code"`
	ExternalResp       interface{} `json:"external_resp"`
}

func (e ErrExternal) Error() string {
	return ""
}

func IsPgDuplicateConstraintError(err error) bool {
	if err == nil {
		return false
	}

	return errors.Is(err, &pgconn.PgError{Code: "23505"})
}
